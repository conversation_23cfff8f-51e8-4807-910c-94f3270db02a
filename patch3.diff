From 3d3a250956289f483d29305fc992850c0b00ac13 Mon Sep 17 00:00:00 2001
From: zhangweiteng <<EMAIL>>
Date: Wed, 30 Jul 2025 13:49:17 +0800
Subject: [PATCH] =?UTF-8?q?[bugfix]=20=E3=80=90=E3=80=90ios=E3=80=91?=
 =?UTF-8?q?=E7=82=B9=E5=87=BB=E6=90=9C=E7=B4=A2=E6=A1=86=E9=9C=80=E8=A6=81?=
 =?UTF-8?q?=E8=B0=83=E6=95=B4=E4=B8=AD=E9=97=B4=E9=A1=B5=E3=80=91=20https:?=
 =?UTF-8?q?//www.tapd.meiyou.com/tapd=5Ffe/21039721/bug/detail/11210397210?=
 =?UTF-8?q?01332314?=
MIME-Version: 1.0
Content-Type: text/plain; charset=UTF-8
Content-Transfer-Encoding: 8bit

---
 .../Source/NavBar/Search/IMYCKSearchHelper.m  | 22 -------------------
 1 <USER> <GROUP>, 22 deletions(-)

diff --git a/IMYCommonKit/Source/NavBar/Search/IMYCKSearchHelper.m b/IMYCommonKit/Source/NavBar/Search/IMYCKSearchHelper.m
index bf490b0..2842a62 100644
--- a/IMYCommonKit/Source/NavBar/Search/IMYCKSearchHelper.m
+++ b/IMYCommonKit/Source/NavBar/Search/IMYCKSearchHelper.m
@@ -173,28 +173,6 @@ static NSTimer *kSearchTimer = nil; //定时器
 }
 
 - (void)searchAction:(BOOL)isSearchBar {
-    // 8.93.0版本：检查当前轮播词是否有scheme_uri，如果有则直接跳转
-    if (isSearchBar) {
-        NSDictionary *currentSearchWordObject = self.currentSearchWordObject;
-        NSString *schemeURI = nil;
-
-        if ([currentSearchWordObject isKindOfClass:[NSDictionary class]]) {
-            NSString *uri = currentSearchWordObject[@"scheme_uri"];
-            if ([uri isKindOfClass:[NSString class]] && uri.length > 0) {
-                schemeURI = uri;
-            }
-        }
-
-        if (schemeURI) {
-            // 有scheme_uri，在跳转前保存搜索历史记录
-            [self saveSearchHistoryBeforeSchemeJump:currentSearchWordObject];
-
-            // 执行跳转
-            [[IMYURIManager shareURIManager] runActionWithString:schemeURI];
-            return;
-        }
-    }
-
     NSMutableDictionary *dic = [@{@"from": @(self.fromType),
                                   @"pos_id": @(self.posId),
                                   @"shouldLoadCache": @(YES),
-- 
2.46.0

