From 9ac2d62c7393a8c998c503e691bc1b6734ec88e3 Mon Sep 17 00:00:00 2001
From: zhangweiteng <<EMAIL>>
Date: Wed, 30 Jul 2025 12:35:53 +0800
Subject: [PATCH] =?UTF-8?q?[bugfix]=20=E3=80=90=E3=80=90BI=E5=8F=8D?=
 =?UTF-8?q?=E9=A6=88=E3=80=91IOS=20=E7=AB=AF=E6=90=9C=E7=B4=A2=E4=B8=AD?=
 =?UTF-8?q?=E9=97=B4=E9=A1=B5=E7=82=B9=E5=87=BB=E4=B8=8A=E6=8A=A5=E6=95=B0?=
 =?UTF-8?q?=E6=8D=AE=E5=BC=82=E5=B8=B8=E3=80=91=20https://www.tapd.meiyou.?=
 =?UTF-8?q?com/tapd=5Ffe/21039721/bug/detail/1121039721001332000?=
MIME-Version: 1.0
Content-Type: text/plain; charset=UTF-8
Content-Transfer-Encoding: 8bit

---
 .../Search/H5/SYSearchResultWithH5VC.m        | 43 +++----------------
 .../Classes/Search/Native/IMYNASearchHomeVC.m | 16 ++-----
 2 files changed, 9 insertions(+), 50 deletions(-)

diff --git a/Seeyou/Classes/Search/H5/SYSearchResultWithH5VC.m b/Seeyou/Classes/Search/H5/SYSearchResultWithH5VC.m
index bbb72f21..7fee7d84 100644
--- a/Seeyou/Classes/Search/H5/SYSearchResultWithH5VC.m
+++ b/Seeyou/Classes/Search/H5/SYSearchResultWithH5VC.m
@@ -76,11 +76,14 @@ static NSString * const IMYSearchResultOnAssociateChangeKey = @"IMYSearchResultO
     [super viewDidLoad];
     self.navigationBarHidden = YES;
     
+    // 是否使用新样式实验
+    self.usingNewSearchStyle = [[[IMYABTestManager sharedInstance] experimentForKey:@"seach_sug_btn"].vars boolForKey:@"btn"];
+
     // 搜索栏样式实验配置
     // 值说明：0-老样式，1-实验组1样式（左返回+右搜索黑色），2-实验组2样式（左返回+右搜索渐变色）
     IMYABTestExperiment *searchStyleExp = [[IMYABTestManager sharedInstance] experimentForKey:@"btnuniify"];
     self.searchBarStyleValue = [searchStyleExp.vars integerForKey:@"btnuniify"];
-    self.usingNewSearchStyle = (self.searchBarStyleValue > 0);
+
     
     // 搜索唯一性标识
     if (!self.search_key.length) {
@@ -209,7 +212,7 @@ static NSString * const IMYSearchResultOnAssociateChangeKey = @"IMYSearchResultO
 - (IMYNAInputSearchBar *)searchBar {
     if (!_searchBar) {
         _searchBar = [IMYNAInputSearchBar new];
-        
+
         // 根据实验值配置搜索栏样式
         if (self.searchBarStyleValue == 1 || self.searchBarStyleValue == 2) {
             // 实验组1和2：使用新的样式配置
@@ -218,7 +221,6 @@ static NSString * const IMYSearchResultOnAssociateChangeKey = @"IMYSearchResultO
             // 老样式：隐藏左按钮
             [_searchBar usingHideLeftButtonStyle];
         }
-        // 其他值且usingNewSearchStyle为YES时，使用默认样式
         _searchBar.imy_width = SCREEN_WIDTH;
         [_searchBar.textField addTarget:self action:@selector(searchAction:) forControlEvents:UIControlEventEditingDidEndOnExit];
         [_searchBar.textField addTarget:self action:@selector(textFieldTextChangeAction:) forControlEvents:UIControlEventEditingChanged];
@@ -628,27 +630,6 @@ static NSString * const IMYSearchResultOnAssociateChangeKey = @"IMYSearchResultO
     searchHomeVC.bi_source = self.bi_source;
     searchHomeVC.info_type = self.info_type;
     searchHomeVC.fromURI = self.fromURI;
-    // 目标控制器
-    IMYNASearchHomeVC *targetController = nil;
-    for (UIViewController *vc in self.navigationController.viewControllers) {
-        if ([vc isKindOfClass:IMYNASearchHomeVC.class]) {
-            targetController = (IMYNASearchHomeVC *)vc;
-            
-        }
-    }
-    if (targetController) {
-        if (targetController.searchPlaceholder) {
-            searchHomeVC.searchPlaceholder = targetController.searchPlaceholder;
-            if ([targetController.searchPlaceholder isKindOfClass:[NSDictionary class]]) {
-                self.searchBar.textField.placeholder = targetController.searchPlaceholder[@"keyword"];
-            } else  if ([targetController.searchPlaceholder isKindOfClass:[NSString class]]) {
-                self.searchBar.textField.placeholder = targetController.searchPlaceholder;
-            }
-        }
-        if (targetController.defaultSearchPlaceholder) {
-            searchHomeVC.defaultSearchPlaceholder = targetController.defaultSearchPlaceholder;
-        }
-    }
     
     // 动画期间暂停用户交互
     UINavigationController * const navVC = self.navigationController;
@@ -685,19 +666,7 @@ static NSString * const IMYSearchResultOnAssociateChangeKey = @"IMYSearchResultO
                           duration:0.1f
                            options:UIViewAnimationOptionTransitionCrossDissolve
                         animations:^{
-            // 需要跳转到搜索首页
-            IMYNASearchHomeVC *targetController = nil;
-            for (UIViewController *vc in navVC.viewControllers) {
-                if ([vc isKindOfClass:IMYNASearchHomeVC.class]) {
-                    targetController = (IMYNASearchHomeVC *)vc;
-                }
-            }
-            if (targetController && [navVC.viewControllers containsObject:targetController]) {
-                // 找到首页，直接返回
-                [navVC popToViewController:targetController animated:NO];
-            } else {
-                [navVC popViewControllerAnimated:NO];
-            }
+            [navVC popViewControllerAnimated:NO];
         } completion:^(BOOL finished){
             //恢复用户交互
             navVC.view.userInteractionEnabled = YES;
diff --git a/Seeyou/Classes/Search/Native/IMYNASearchHomeVC.m b/Seeyou/Classes/Search/Native/IMYNASearchHomeVC.m
index 2201655e..6735af2b 100644
--- a/Seeyou/Classes/Search/Native/IMYNASearchHomeVC.m
+++ b/Seeyou/Classes/Search/Native/IMYNASearchHomeVC.m
@@ -413,7 +413,8 @@ IMY_KYLIN_FUNC_MAINTAB_ASYNC {
     
     // 键盘遮挡曝光
     self.imyut_exposureStatus.enableKeyboardCoverOut = YES;
-    
+    // 是否搜索首页
+    self.isSearchHomePage = self.navigationController.viewControllers.count <= 1;
     // 搜索首页才有的逻辑
     if (self.isSearchHomePage) {
         // 入口页点击搜索框埋点。 反正点击后都是进入这个页面，所以就放这里埋
@@ -1023,8 +1024,6 @@ IMY_KYLIN_FUNC_MAINTAB_ASYNC {
         [self.searchBar.textField becomeFirstResponder];
         self.isSearchBarInputing = NO;
     }
-    // 是否搜索首页
-    self.isSearchHomePage = self.navigationController.viewControllers.count <= 1 || [self isArrayContainsOnlySpecifiedViewControllers:self.navigationController.viewControllers];
 }
 
 - (void)viewWillDisappear:(BOOL)animated {
@@ -1947,15 +1946,6 @@ IMY_KYLIN_FUNC_MAINTAB_ASYNC {
 
 #pragma mark - Private Methods
 
-// 只有搜索页和搜索结果页，则判断为搜索首页
-- (BOOL)isArrayContainsOnlySpecifiedViewControllers:(NSArray *)viewControllers {
-    for (id controller in viewControllers) {
-        if (![controller isKindOfClass:[IMYNASearchHomeVC class]] &&
-            ![controller isKindOfClass:[SYSearchResultWithH5VC class]]) {
-            return NO;
-        }
-    }
-    return YES;
-}
+
 
 @end
-- 
2.46.0

