From 6544d8967a5340e20e35c9dced0572f5ef2daf92 Mon Sep 17 00:00:00 2001
From: zhangweiteng <<EMAIL>>
Date: Wed, 30 Jul 2025 12:37:25 +0800
Subject: [PATCH] =?UTF-8?q?[bugfix]=20=E3=80=90=E3=80=90ios=E3=80=91?=
 =?UTF-8?q?=E8=BE=93=E5=85=A5=E6=A1=86=E7=82=B9=E5=87=BB=E6=B8=85=E9=99=A4?=
 =?UTF-8?q?=E5=90=8E=EF=BC=8C=E6=9C=AA=E5=B1=95=E7=A4=BAicon=E3=80=91=20ht?=
 =?UTF-8?q?tps://www.tapd.meiyou.com/tapd=5Ffe/21039721/bug/detail/1121039?=
 =?UTF-8?q?721001332397?=
MIME-Version: 1.0
Content-Type: text/plain; charset=UTF-8
Content-Transfer-Encoding: 8bit

---
 Seeyou/Classes/Search/Native/IMYNASearchHomeVC.m | 3 +++
 1 file changed, 3 insertions(+)

diff --git a/Seeyou/Classes/Search/Native/IMYNASearchHomeVC.m b/Seeyou/Classes/Search/Native/IMYNASearchHomeVC.m
index 6735af2b..d1324cbd 100644
--- a/Seeyou/Classes/Search/Native/IMYNASearchHomeVC.m
+++ b/Seeyou/Classes/Search/Native/IMYNASearchHomeVC.m
@@ -1147,6 +1147,9 @@ IMY_KYLIN_FUNC_MAINTAB_ASYNC {
         self.associateVC.view.hidden = YES;
         self.lastTextFiledText = nil;
     }
+    if (self.originalIconType > 0) {
+        [self.searchBar updateIconTagWithType:self.originalIconType];
+    }
 }
 
 /// UIControlEventEditingChanged
-- 
2.46.0

